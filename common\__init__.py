# common/__init__.py
"""
Common utilities and shared resources for WOSS Seismic Analysis Tool.

This package contains GPU-optimized constants, session state management,
and shared UI components for the modular Streamlit application.
"""

# Import core modules
from . import constants

# Import other modules when they are created
try:
    from . import session_state
    from . import ui_elements
except ImportError:
    # Modules will be created in subsequent steps
    pass

__all__ = [
    'constants',
    'session_state', 
    'ui_elements'
]
