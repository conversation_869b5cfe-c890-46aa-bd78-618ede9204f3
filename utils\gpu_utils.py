# utils/gpu_utils.py
import logging
import streamlit as st

# Global GPU state
GPU_AVAILABLE = False
CPU_FALLBACK_ENABLED = True

@st.cache_resource
def initialize_gpu_system():
    """Initialize GPU processing with fallback detection."""
    global GPU_AVAILABLE
    
    try:
        import cupy as cp
        # Test GPU functionality
        test_array = cp.array([1, 2, 3])
        test_result = cp.sum(test_array)
        
        GPU_AVAILABLE = True
        logging.info("GPU processing enabled - CuPy available")
        return True, "CuPy", cp.cuda.Device().compute_capability
        
    except ImportError:
        logging.warning("CuPy not available - using CPU fallback")
        GPU_AVAILABLE = False
        return False, "CPU Fallback", None
    except Exception as e:
        logging.error(f"GPU initialization failed: {e}")
        GPU_AVAILABLE = False
        return False, "CPU Fallback", str(e)

def get_processing_backend():
    """Get the active processing backend (GPU or CPU)."""
    if GPU_AVAILABLE:
        try:
            import cupy as cp
            from utils.dlogst_spec_descriptor_gpu import dlogst_spec_descriptor_gpu
            return "GPU", dlogst_spec_descriptor_gpu
        except ImportError:
            pass
    
    # Fallback to CPU
    from utils.dlogst_spec_descriptor_cpu import dlogst_spec_descriptor_cpu
    return "CPU", dlogst_spec_descriptor_cpu

def optimize_batch_size_for_mode(mode, base_size=512):
    """Optimize batch size based on analysis mode and GPU memory."""
    if not GPU_AVAILABLE:
        return min(base_size // 4, 128)  # Conservative CPU batch size
    
    try:
        import cupy as cp
        mempool = cp.get_default_memory_pool()
        free_bytes = mempool.free_bytes()
        total_bytes = mempool.total_bytes()
        
        # Mode-specific optimization
        if mode in ["Single inline (all crosslines)", "Single crossline (all inlines)"]:
            return min(base_size * 2, 1024)  # Larger batches for line processing
        elif mode == "By inline/crossline section (AOI)":
            return base_size  # Standard batch size for AOI
        elif mode == "By Polyline File Import":
            return min(base_size // 2, 256)  # Smaller batches for complex polyline processing
        else:
            return base_size
            
    except Exception:
        return base_size
