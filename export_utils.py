import tkinter as tk
from tkinter import messagebox
import numpy as np

class AttributeSelector:
    def __init__(self, root, exportable_attrs):
        self.root = root
        self.exportable_attrs = exportable_attrs
        self.selected_attrs = []

    def show_dialog(self):
        attrs_window = tk.Toplevel(self.root)
        attrs_window.title("Select Attributes to Export")
        attrs_window.geometry("400x450")

        tk.Label(attrs_window, text="Select spectral attributes to export to SEG-Y:").pack(pady=5)
        frame = tk.Frame(attrs_window)
        frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        scrollbar = tk.Scrollbar(frame, orient="vertical")
        listbox = tk.Listbox(frame, selectmode=tk.MULTIPLE, yscrollcommand=scrollbar.set, width=40, height=15)
        scrollbar.config(command=listbox.yview)
        scrollbar.pack(side="right", fill="y")
        listbox.pack(side="left", fill="both", expand=True)

        for attr in self.exportable_attrs:
            listbox.insert(tk.END, attr)

        def on_ok():
            self.selected_attrs = [self.exportable_attrs[i] for i in listbox.curselection()]
            attrs_window.destroy()

        tk.Button(attrs_window, text="OK", command=on_ok).pack(pady=10)
        attrs_window.wait_window()

        return self.selected_attrs

def select_export_attributes(root, desired_attrs, first_descriptor, sample_length):
    """
    Creates a dialog for selecting attributes to export to SEG-Y files.
    Filters only feasible attributes for export, matching logic from 10b_3D_WOSS_InitEq_GPU_ILXL_Merged.py.
    Returns a list of selected attribute names.
    """
    # Filter exportable attributes as in 10b
    exportable_attrs = [
        key for key in desired_attrs
        if key in first_descriptor and isinstance(first_descriptor[key], np.ndarray) and len(first_descriptor[key]) == sample_length
    ]
    # Optionally add WOSS if possible
    if (
        'hfc' in first_descriptor and
        'norm_fdom' in first_descriptor and
        'mag_voice_slope' in first_descriptor
    ):
        exportable_attrs.append('WOSS')

    # Create a dialog window
    dialog = tk.Toplevel(root)
    dialog.title("Select Attributes to Export")
    dialog.geometry("400x300")
    dialog.resizable(False, False)
    dialog.grab_set()  # Make the dialog modal

    tk.Label(dialog, text="Select spectral attributes to export to SEG-Y:", pady=10).pack()

    listbox = tk.Listbox(dialog, selectmode=tk.MULTIPLE, width=40, height=15)
    for attr in exportable_attrs:
        listbox.insert(tk.END, attr)
    listbox.pack(padx=10, pady=5, fill=tk.BOTH, expand=True)

    result = []
    def on_ok():
        nonlocal result
        selected_indices = listbox.curselection()
        if not selected_indices:
            messagebox.showwarning("Warning", "No attributes selected. Please select at least one attribute.")
            return
        result = [listbox.get(i) for i in selected_indices]
        dialog.destroy()

    tk.Button(dialog, text="OK", command=on_ok, width=10).pack(pady=10)
    dialog.wait_window()
    return result